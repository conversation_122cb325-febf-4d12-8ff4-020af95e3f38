import React, { forwardRef } from "react";

/**
 * Reusable Input Component
 * Supports different types, validation states, and error messages
 */
const Input = forwardRef(
  (
    {
      label,
      type = "text",
      placeholder = "",
      value = "",
      onChange,
      onBlur,
      error = null,
      required = false,
      disabled = false,
      className = "",
      inputClassName = "",
      id,
      name,
      ...props
    },
    ref
  ) => {
    const inputId =
      id || name || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = error && error.length > 0;

    const baseInputClasses =
      "w-full px-3 py-2 border rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-0 transition-all duration-200 transform focus:scale-[1.02]";

    const stateClasses = hasError
      ? "border-red-300 focus:ring-red-500 focus:border-red-500 focus:shadow-red-100"
      : "border-gray-300 focus:ring-blue-500 focus:border-blue-500 focus:shadow-blue-100";

    const disabledClasses = disabled
      ? "bg-gray-100 cursor-not-allowed opacity-60"
      : "bg-white hover:border-gray-400 hover:shadow-sm";

    return (
      <div className={`space-y-1 ${className}`}>
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700"
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <input
          ref={ref}
          id={inputId}
          name={name}
          type={type}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`${baseInputClasses} ${stateClasses} ${disabledClasses} ${inputClassName}`}
          {...props}
        />

        {hasError && (
          <div className="text-red-600 text-sm space-y-1">
            {Array.isArray(error) ? (
              error.map((err, index) => <p key={index}>{err}</p>)
            ) : (
              <p>{error}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

/**
 * Textarea Component
 * For multi-line text input
 */
export const Textarea = forwardRef(
  (
    {
      label,
      placeholder = "",
      value = "",
      onChange,
      onBlur,
      error = null,
      required = false,
      disabled = false,
      rows = 3,
      className = "",
      textareaClassName = "",
      id,
      name,
      ...props
    },
    ref
  ) => {
    const textareaId =
      id || name || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = error && error.length > 0;

    const baseTextareaClasses =
      "w-full px-3 py-2 border rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors duration-200 resize-vertical";

    const stateClasses = hasError
      ? "border-red-300 focus:ring-red-500 focus:border-red-500"
      : "border-gray-300 focus:ring-blue-500 focus:border-blue-500";

    const disabledClasses = disabled
      ? "bg-gray-100 cursor-not-allowed opacity-60"
      : "bg-white hover:border-gray-400";

    return (
      <div className={`space-y-1 ${className}`}>
        {label && (
          <label
            htmlFor={textareaId}
            className="block text-sm font-medium text-gray-700"
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <textarea
          ref={ref}
          id={textareaId}
          name={name}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          rows={rows}
          className={`${baseTextareaClasses} ${stateClasses} ${disabledClasses} ${textareaClassName}`}
          {...props}
        />

        {hasError && (
          <div className="text-red-600 text-sm space-y-1">
            {Array.isArray(error) ? (
              error.map((err, index) => <p key={index}>{err}</p>)
            ) : (
              <p>{error}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = "Textarea";

/**
 * Select Component
 * For dropdown selections
 */
export const Select = forwardRef(
  (
    {
      label,
      value = "",
      onChange,
      onBlur,
      options = [],
      error = null,
      required = false,
      disabled = false,
      placeholder = "Select an option",
      className = "",
      selectClassName = "",
      id,
      name,
      ...props
    },
    ref
  ) => {
    const selectId =
      id || name || `select-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = error && error.length > 0;

    const baseSelectClasses =
      "w-full px-3 py-2 border rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors duration-200";

    const stateClasses = hasError
      ? "border-red-300 focus:ring-red-500 focus:border-red-500"
      : "border-gray-300 focus:ring-blue-500 focus:border-blue-500";

    const disabledClasses = disabled
      ? "bg-gray-100 cursor-not-allowed opacity-60"
      : "bg-white hover:border-gray-400";

    return (
      <div className={`space-y-1 ${className}`}>
        {label && (
          <label
            htmlFor={selectId}
            className="block text-sm font-medium text-gray-700"
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <select
          ref={ref}
          id={selectId}
          name={name}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          disabled={disabled}
          required={required}
          className={`${baseSelectClasses} ${stateClasses} ${disabledClasses} ${selectClassName}`}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {hasError && (
          <div className="text-red-600 text-sm space-y-1">
            {Array.isArray(error) ? (
              error.map((err, index) => <p key={index}>{err}</p>)
            ) : (
              <p>{error}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Select.displayName = "Select";

export default Input;
