import React from 'react';
import { InlineSpinner } from './LoadingSpinner';

/**
 * Reusable Button Component
 * Supports different variants, sizes, and loading states
 */
const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  type = 'button',
  className = '',
  onClick,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',
    ghost: 'text-blue-600 hover:bg-blue-50 focus:ring-blue-500',
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  };

  const isDisabled = disabled || loading;

  const handleClick = (e) => {
    if (!isDisabled && onClick) {
      onClick(e);
    }
  };

  return (
    <button
      type={type}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      disabled={isDisabled}
      onClick={handleClick}
      {...props}
    >
      {loading && (
        <InlineSpinner 
          size={size === 'sm' ? 'xs' : 'sm'} 
          color="white" 
        />
      )}
      <span className={loading ? 'ml-2' : ''}>
        {children}
      </span>
    </button>
  );
};

/**
 * Icon Button Component
 * For buttons with only icons
 */
export const IconButton = ({
  children,
  variant = 'ghost',
  size = 'md',
  className = '',
  ...props
}) => {
  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3',
  };

  return (
    <Button
      variant={variant}
      className={`${sizeClasses[size]} ${className}`}
      {...props}
    >
      {children}
    </Button>
  );
};

/**
 * Button Group Component
 * For grouping related buttons
 */
export const ButtonGroup = ({ children, className = '' }) => {
  return (
    <div className={`inline-flex rounded-md shadow-sm ${className}`}>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;
        
        const isFirst = index === 0;
        const isLast = index === React.Children.count(children) - 1;
        
        let groupClasses = '';
        if (!isFirst && !isLast) {
          groupClasses = 'rounded-none border-l-0';
        } else if (isFirst) {
          groupClasses = 'rounded-r-none';
        } else if (isLast) {
          groupClasses = 'rounded-l-none border-l-0';
        }
        
        return React.cloneElement(child, {
          className: `${child.props.className || ''} ${groupClasses}`.trim(),
        });
      })}
    </div>
  );
};

export default Button;
