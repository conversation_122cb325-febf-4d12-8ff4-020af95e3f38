import React from 'react';
import { Link } from 'react-router-dom';

/**
 * Floating Action Button Component
 * Modern FAB with smooth animations and hover effects
 */
const FloatingActionButton = ({ 
  to, 
  onClick, 
  icon, 
  tooltip = '', 
  className = '',
  variant = 'primary',
  size = 'md',
  ...props 
}) => {
  const baseClasses = 'fixed bottom-6 right-6 rounded-full shadow-2xl transition-all duration-300 transform hover:scale-110 active:scale-95 z-50 flex items-center justify-center font-medium focus:outline-none focus:ring-4 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'gradient-bg-primary text-white hover:shadow-blue-300 focus:ring-blue-500',
    success: 'gradient-bg-success text-white hover:shadow-green-300 focus:ring-green-500',
    secondary: 'gradient-bg-secondary text-white hover:shadow-pink-300 focus:ring-pink-500',
  };
  
  const sizeClasses = {
    sm: 'w-12 h-12 text-sm',
    md: 'w-14 h-14 text-base',
    lg: 'w-16 h-16 text-lg',
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  const content = (
    <>
      {icon}
      {tooltip && (
        <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
          {tooltip}
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </>
  );

  if (to) {
    return (
      <Link to={to} className={`group ${buttonClasses}`} {...props}>
        {content}
      </Link>
    );
  }

  return (
    <button onClick={onClick} className={`group ${buttonClasses}`} {...props}>
      {content}
    </button>
  );
};

/**
 * Quick Action FAB for adding new student
 */
export const AddStudentFAB = () => {
  return (
    <FloatingActionButton
      to="/form"
      tooltip="Add New Student"
      variant="primary"
      size="lg"
      className="pulse-glow"
      icon={
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      }
    />
  );
};

/**
 * Scroll to top FAB
 */
export const ScrollToTopFAB = ({ show = true }) => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (!show) return null;

  return (
    <FloatingActionButton
      onClick={scrollToTop}
      tooltip="Scroll to Top"
      variant="secondary"
      size="md"
      className="bottom-24"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      }
    />
  );
};

export default FloatingActionButton;
