import { NavLink } from "react-router-dom";

function Navbar() {
  const navLinkClass = ({ isActive }) =>
    `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
      isActive
        ? "bg-blue-700 text-white"
        : "text-blue-100 hover:bg-blue-500 hover:text-white"
    }`;

  return (
    <nav className="gradient-bg-primary shadow-2xl relative overflow-hidden">
      <div className="absolute inset-0 bg-black opacity-10"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex justify-between items-center h-16">
          {/* Logo/Brand */}
          <div className="flex-shrink-0">
            <NavLink
              to="/home"
              className="text-white text-xl font-bold hover:text-blue-100 transition-all duration-300 transform hover:scale-105"
            >
              <span className="flex items-center">
                <svg
                  className="w-8 h-8 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
                Student Application System
              </span>
            </NavLink>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <NavLink to="/home" className={navLinkClass}>
                Home
              </NavLink>
              <NavLink to="/form" className={navLinkClass}>
                Registration
              </NavLink>
              <NavLink to="/students" className={navLinkClass}>
                Applications
              </NavLink>
              <NavLink to="/about" className={navLinkClass}>
                About
              </NavLink>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="bg-blue-600 inline-flex items-center justify-center p-2 rounded-md text-blue-200 hover:text-white hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-600 focus:ring-white"
              aria-controls="mobile-menu"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {/* Menu icon */}
              <svg
                className="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="md:hidden" id="mobile-menu">
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-700">
          <NavLink
            to="/home"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                isActive
                  ? "bg-blue-800 text-white"
                  : "text-blue-100 hover:bg-blue-600 hover:text-white"
              }`
            }
          >
            Home
          </NavLink>
          <NavLink
            to="/form"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                isActive
                  ? "bg-blue-800 text-white"
                  : "text-blue-100 hover:bg-blue-600 hover:text-white"
              }`
            }
          >
            Registration
          </NavLink>
          <NavLink
            to="/students"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                isActive
                  ? "bg-blue-800 text-white"
                  : "text-blue-100 hover:bg-blue-600 hover:text-white"
              }`
            }
          >
            Applications
          </NavLink>
          <NavLink
            to="/about"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                isActive
                  ? "bg-blue-800 text-white"
                  : "text-blue-100 hover:bg-blue-600 hover:text-white"
              }`
            }
          >
            About
          </NavLink>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
