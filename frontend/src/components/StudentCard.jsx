import { useState } from "react";
import Card, {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "./ui/Card";
import Button, { ButtonGroup } from "./ui/Button";
import ConfirmDialog from "./ui/ConfirmDialog";
import { useDeleteStudent } from "../hooks/useStudents";
import { formatFieldName } from "../utils/validation";

/**
 * Student Card Component
 * Displays individual student information with actions
 */
const StudentCard = ({ student, onEdit, onView }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { mutate: deleteStudent, isPending: isDeleting } = useDeleteStudent();

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    deleteStudent(student.student_id);
    setShowDeleteD<PERSON>og(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ""}${
      lastName?.charAt(0) || ""
    }`.toUpperCase();
  };

  return (
    <Card className="modern-card hover-lift transition-all duration-300">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {/* Avatar */}
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
              {getInitials(student.first_name, student.last_name)}
            </div>

            {/* Basic Info */}
            <div>
              <CardTitle size="md" className="text-gray-900">
                {student.first_name} {student.last_name}
              </CardTitle>
              <p className="text-sm text-gray-600">{student.email}</p>
              <p className="text-xs text-gray-500">
                Applied: {formatDate(student.application_date)}
              </p>
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex flex-col items-end space-y-2">
            <span className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
              Active
            </span>
            <span className="text-lg font-bold text-blue-600">
              {student.marks_percentage}%
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Quick Info */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide">
              Course
            </p>
            <p className="text-sm font-medium text-gray-900 truncate">
              {student.course_applied_for}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide">
              Qualification
            </p>
            <p className="text-sm font-medium text-gray-900 truncate">
              {student.qualification}
            </p>
          </div>
        </div>

        {/* Expandable Details */}
        {showDetails && (
          <div className="border-t pt-4 space-y-3 fade-in">
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wide">
                Contact
              </p>
              <p className="text-sm text-gray-900">{student.phone_number}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wide">
                Date of Birth
              </p>
              <p className="text-sm text-gray-900">
                {formatDate(student.date_of_birth)}
              </p>
            </div>
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wide">
                Gender
              </p>
              <p className="text-sm text-gray-900">{student.gender}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wide">
                Address
              </p>
              <p className="text-sm text-gray-900">{student.address}</p>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-between items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="text-blue-600 hover:text-blue-700"
        >
          {showDetails ? (
            <>
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 15l7-7 7 7"
                />
              </svg>
              Less
            </>
          ) : (
            <>
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
              More
            </>
          )}
        </Button>

        <ButtonGroup>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView?.(student)}
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            View
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit?.(student)}
            className="text-green-600 border-green-600 hover:bg-green-50"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
            Edit
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            loading={isDeleting}
            disabled={isDeleting}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
            Delete
          </Button>
        </ButtonGroup>
      </CardFooter>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="Delete Student Application"
        message={`Are you sure you want to delete ${student.first_name} ${student.last_name}'s application? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        loading={isDeleting}
      />
    </Card>
  );
};

export default StudentCard;
