import { useState, useEffect } from "react";
import <PERSON>, {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "./ui/Card";
import Button from "./ui/Button";
import Input, { Textarea, Select } from "./ui/Input";
import { useUpdateStudent } from "../hooks/useStudents";
import { validateForm, formatFieldName } from "../utils/validation";
import { FORM_FIELDS } from "../utils/constants";

/**
 * Student Modal Component
 * For viewing and editing student details
 */
const StudentModal = ({ student, isOpen, onClose, mode = "view" }) => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isEditing, setIsEditing] = useState(mode === "edit");

  const { mutate: updateStudent, isPending } = useUpdateStudent({
    onSuccess: (updatedStudent) => {
      // Update the form data with the latest student data
      setFormData(updatedStudent);
      setIsEditing(false);
      onClose();
    },
  });

  useEffect(() => {
    if (student) {
      setFormData(student);
      setErrors({});
      setTouched({});
      setIsEditing(mode === "edit");
    }
  }, [student, mode]);

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!isEditing) return;

    const { errors: validationErrors, hasErrors } = validateForm(formData);

    if (hasErrors) {
      setErrors(validationErrors);
      const allTouched = Object.keys(formData).reduce((acc, key) => {
        acc[key] = true;
        return acc;
      }, {});
      setTouched(allTouched);
      return;
    }

    setErrors({});
    updateStudent({ id: student.student_id, data: formData });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  const handleBlur = (e) => {
    const { name } = e.target;
    setTouched((prev) => ({ ...prev, [name]: true }));
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const renderField = (fieldName, config) => {
    const value = formData[fieldName] || "";

    if (!isEditing) {
      // View mode - display formatted values
      let displayValue = value;
      if (fieldName === "date_of_birth" || fieldName === "application_date") {
        displayValue = formatDate(value);
      }

      return (
        <div key={fieldName} className="space-y-1">
          <label className="block text-sm font-medium text-gray-700">
            {config.label}
          </label>
          <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
            {displayValue || "N/A"}
          </p>
        </div>
      );
    }

    // Edit mode - render input fields
    const commonProps = {
      name: fieldName,
      value: value,
      onChange: handleChange,
      onBlur: handleBlur,
      error: touched[fieldName] ? errors[fieldName] : null,
      required: config.required,
      placeholder: config.placeholder,
      disabled: isPending,
    };

    switch (config.type) {
      case "textarea":
        return (
          <Textarea
            key={fieldName}
            label={config.label}
            rows={config.rows}
            maxLength={config.maxLength}
            {...commonProps}
          />
        );

      case "select":
        return (
          <Select
            key={fieldName}
            label={config.label}
            options={config.options}
            {...commonProps}
          />
        );

      default:
        return (
          <Input
            key={fieldName}
            label={config.label}
            type={config.type}
            min={config.min}
            max={config.max}
            step={config.step}
            maxLength={config.maxLength}
            {...commonProps}
          />
        );
    }
  };

  if (!isOpen || !student) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <Card className="h-full border-0 shadow-none">
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {student.first_name?.charAt(0)}
                  {student.last_name?.charAt(0)}
                </div>
                <div>
                  <CardTitle className="text-xl">
                    {isEditing ? "Edit Student" : "Student Details"}
                  </CardTitle>
                  <p className="text-gray-600">
                    {student.first_name} {student.last_name}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {!isEditing && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                  >
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                    Edit
                  </Button>
                )}

                <Button variant="ghost" size="sm" onClick={onClose}>
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="overflow-y-auto max-h-[60vh] p-6">
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(FORM_FIELDS).map(([fieldName, config]) => {
                  const fullWidthFields = ["address", "course_applied_for"];
                  const isFullWidth = fullWidthFields.includes(fieldName);

                  return (
                    <div
                      key={fieldName}
                      className={isFullWidth ? "md:col-span-2" : ""}
                    >
                      {renderField(fieldName, config)}
                    </div>
                  );
                })}
              </div>
            </form>
          </CardContent>

          <CardFooter className="border-t flex justify-end space-x-3">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditing(false);
                    setFormData(student);
                    setErrors({});
                    setTouched({});
                  }}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSubmit}
                  loading={isPending}
                  disabled={isPending}
                >
                  Save Changes
                </Button>
              </>
            ) : (
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default StudentModal;
