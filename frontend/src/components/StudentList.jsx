import { useState, useMemo } from "react";
import { useStudents } from "../hooks/useStudents";
import StudentCard from "./StudentCard";
import Input from "./ui/Input";
import { Select } from "./ui/Input";
import Button from "./ui/Button";
import LoadingSpinner, { LoadingSkeleton } from "./ui/LoadingSpinner";
import Card, { CardHeader, CardTitle, CardContent } from "./ui/Card";

/**
 * Student List Component
 * Displays all students with search, filter, and pagination
 */
const StudentList = ({ onStudentView, onStudentEdit }) => {
  const { data: students = [], isLoading, error, refetch } = useStudents();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [filterBy, setFilterBy] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const studentsPerPage = 6;

  // Filter and search logic
  const filteredStudents = useMemo(() => {
    if (!students) return [];

    let filtered = [...students];

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (student) =>
          student.first_name?.toLowerCase().includes(term) ||
          student.last_name?.toLowerCase().includes(term) ||
          student.email?.toLowerCase().includes(term) ||
          student.course_applied_for?.toLowerCase().includes(term) ||
          student.qualification?.toLowerCase().includes(term)
      );
    }

    // Course filter
    if (filterBy !== "all") {
      filtered = filtered.filter((student) =>
        student.course_applied_for
          ?.toLowerCase()
          .includes(filterBy.toLowerCase())
      );
    }

    // Sort logic
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return `${a.first_name} ${a.last_name}`.localeCompare(
            `${b.first_name} ${b.last_name}`
          );
        case "date":
          return new Date(b.application_date) - new Date(a.application_date);
        case "marks":
          return (b.marks_percentage || 0) - (a.marks_percentage || 0);
        case "course":
          return (a.course_applied_for || "").localeCompare(
            b.course_applied_for || ""
          );
        default:
          return 0;
      }
    });

    return filtered;
  }, [students, searchTerm, sortBy, filterBy]);

  // Pagination logic
  const totalPages = Math.ceil(filteredStudents.length / studentsPerPage);
  const startIndex = (currentPage - 1) * studentsPerPage;
  const paginatedStudents = filteredStudents.slice(
    startIndex,
    startIndex + studentsPerPage
  );

  // Get unique courses for filter dropdown
  const uniqueCourses = useMemo(() => {
    if (!students) return [];
    const courses = [
      ...new Set(students.map((s) => s.course_applied_for).filter(Boolean)),
    ];
    return courses.map((course) => ({ value: course, label: course }));
  }, [students]);

  const handleRefresh = () => {
    refetch();
  };

  const handleExport = () => {
    // Simple CSV export
    const csvContent = [
      [
        "Name",
        "Email",
        "Course",
        "Qualification",
        "Marks %",
        "Application Date",
      ].join(","),
      ...filteredStudents.map((student) =>
        [
          `"${student.first_name} ${student.last_name}"`,
          student.email,
          `"${student.course_applied_for}"`,
          `"${student.qualification}"`,
          student.marks_percentage,
          student.application_date,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `students_${new Date().toISOString().split("T")[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="h-64">
              <LoadingSkeleton lines={4} className="p-6" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="text-center py-12 border-red-200 bg-red-50">
        <div className="text-red-500 mb-4">
          <svg
            className="w-12 h-12 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-red-800 mb-2">
          Failed to load students
        </h3>
        <p className="text-red-600 mb-4">{error.message}</p>
        <Button onClick={handleRefresh} variant="danger">
          Try Again
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <Card className="modern-card">
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
              <CardTitle>Student Applications</CardTitle>
              <p className="text-gray-600 mt-1">
                {filteredStudents.length} of {students.length} students
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Refresh
              </Button>

              <Button onClick={handleExport} variant="success" size="sm">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Export CSV
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <Input
              placeholder="Search students..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="lg:col-span-2"
            />

            {/* Sort */}
            <Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              options={[
                { value: "name", label: "Sort by Name" },
                { value: "date", label: "Sort by Date" },
                { value: "marks", label: "Sort by Marks" },
                { value: "course", label: "Sort by Course" },
              ]}
            />

            {/* Filter */}
            <Select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              options={[
                { value: "all", label: "All Courses" },
                ...uniqueCourses,
              ]}
            />
          </div>
        </CardContent>
      </Card>

      {/* Students Grid */}
      {filteredStudents.length === 0 ? (
        <Card className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg
              className="w-16 h-16 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No students found
          </h3>
          <p className="text-gray-600">
            {searchTerm || filterBy !== "all"
              ? "Try adjusting your search or filter criteria."
              : "No student applications have been submitted yet."}
          </p>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {paginatedStudents.map((student, index) => (
              <div
                key={student.student_id}
                className="fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <StudentCard
                  student={student}
                  onView={onStudentView}
                  onEdit={onStudentEdit}
                />
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </Button>

              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default StudentList;
