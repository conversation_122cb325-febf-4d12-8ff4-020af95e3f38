/**
 * Client-side validation utilities
 * Matches backend validation rules for consistency
 */

/**
 * Validation error class
 */
export class ValidationError extends Error {
  constructor(field, message) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

/**
 * Validation rules and functions
 */
export const validationRules = {
  /**
   * Required field validation
   */
  required: (value, fieldName) => {
    if (!value || (typeof value === 'string' && value.trim().length === 0)) {
      throw new ValidationError(fieldName, `${fieldName} is required`);
    }
    return true;
  },

  /**
   * Email format validation
   */
  email: (value, fieldName = 'Email') => {
    if (!value) return true; // Allow empty if not required
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      throw new ValidationError(fieldName, 'Invalid email format');
    }
    return true;
  },

  /**
   * Marks percentage validation (0-100)
   */
  marksPercentage: (value, fieldName = 'Marks percentage') => {
    if (value === undefined || value === null || value === '') return true;
    
    const marks = parseFloat(value);
    if (isNaN(marks) || marks < 0 || marks > 100) {
      throw new ValidationError(fieldName, 'Marks percentage must be between 0 and 100');
    }
    return true;
  },

  /**
   * Date of birth validation (age 16-100)
   */
  dateOfBirth: (value, fieldName = 'Date of birth') => {
    if (!value) return true;
    
    const dob = new Date(value);
    const today = new Date();
    const age = today.getFullYear() - dob.getFullYear();
    
    if (isNaN(dob.getTime())) {
      throw new ValidationError(fieldName, 'Invalid date format');
    }
    
    if (age < 16 || age > 100) {
      throw new ValidationError(fieldName, 'Age must be between 16 and 100 years');
    }
    return true;
  },

  /**
   * Gender validation
   */
  gender: (value, fieldName = 'Gender') => {
    if (!value) return true;
    
    const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say'];
    if (!validGenders.includes(value)) {
      throw new ValidationError(fieldName, 'Gender must be one of: Male, Female, Other, Prefer not to say');
    }
    return true;
  },

  /**
   * Phone number validation (basic)
   */
  phoneNumber: (value, fieldName = 'Phone number') => {
    if (!value) return true;
    
    // Basic phone number validation (digits, spaces, hyphens, parentheses, plus)
    const phoneRegex = /^[\d\s\-\(\)\+]+$/;
    if (!phoneRegex.test(value) || value.replace(/\D/g, '').length < 10) {
      throw new ValidationError(fieldName, 'Invalid phone number format');
    }
    return true;
  },

  /**
   * String length validation
   */
  maxLength: (maxLength) => (value, fieldName) => {
    if (!value) return true;
    
    if (value.length > maxLength) {
      throw new ValidationError(fieldName, `${fieldName} must be ${maxLength} characters or less`);
    }
    return true;
  },

  /**
   * String minimum length validation
   */
  minLength: (minLength) => (value, fieldName) => {
    if (!value) return true;
    
    if (value.length < minLength) {
      throw new ValidationError(fieldName, `${fieldName} must be at least ${minLength} characters`);
    }
    return true;
  },
};

/**
 * Student form validation schema
 */
export const studentValidationSchema = {
  first_name: [
    validationRules.required,
    validationRules.maxLength(50),
  ],
  last_name: [
    validationRules.required,
    validationRules.maxLength(50),
  ],
  date_of_birth: [
    validationRules.required,
    validationRules.dateOfBirth,
  ],
  gender: [
    validationRules.required,
    validationRules.gender,
  ],
  email: [
    validationRules.required,
    validationRules.email,
    validationRules.maxLength(255),
  ],
  phone_number: [
    validationRules.required,
    validationRules.phoneNumber,
    validationRules.maxLength(15),
  ],
  address: [
    validationRules.required,
    validationRules.maxLength(500),
  ],
  course_applied_for: [
    validationRules.required,
    validationRules.maxLength(100),
  ],
  qualification: [
    validationRules.required,
    validationRules.maxLength(100),
  ],
  marks_percentage: [
    validationRules.required,
    validationRules.marksPercentage,
  ],
  application_date: [
    // Optional field, will be set by backend if not provided
  ],
};

/**
 * Validate a single field
 */
export function validateField(fieldName, value, schema = studentValidationSchema) {
  const rules = schema[fieldName] || [];
  const errors = [];

  for (const rule of rules) {
    try {
      rule(value, fieldName.replace(/_/g, ' '));
    } catch (error) {
      if (error instanceof ValidationError) {
        errors.push(error.message);
      }
    }
  }

  return errors;
}

/**
 * Validate entire form data
 */
export function validateForm(formData, schema = studentValidationSchema) {
  const errors = {};
  let hasErrors = false;

  for (const [fieldName, value] of Object.entries(formData)) {
    const fieldErrors = validateField(fieldName, value, schema);
    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors;
      hasErrors = true;
    }
  }

  return { errors, hasErrors };
}

/**
 * Format field name for display
 */
export function formatFieldName(fieldName) {
  return fieldName
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
}
