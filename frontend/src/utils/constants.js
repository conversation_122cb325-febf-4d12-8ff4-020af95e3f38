/**
 * Application constants
 * Centralized configuration and constant values
 */

/**
 * Gender options for form dropdowns
 */
export const GENDER_OPTIONS = [
  { value: '', label: 'Select Gender' },
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' },
  { value: 'Other', label: 'Other' },
  { value: 'Prefer not to say', label: 'Prefer not to say' },
];

/**
 * Common course options (can be extended)
 */
export const COURSE_OPTIONS = [
  { value: '', label: 'Select Course' },
  { value: 'Computer Science', label: 'Computer Science' },
  { value: 'Information Technology', label: 'Information Technology' },
  { value: 'Electronics Engineering', label: 'Electronics Engineering' },
  { value: 'Mechanical Engineering', label: 'Mechanical Engineering' },
  { value: 'Civil Engineering', label: 'Civil Engineering' },
  { value: 'Business Administration', label: 'Business Administration' },
  { value: 'Commerce', label: 'Commerce' },
  { value: 'Arts', label: 'Arts' },
  { value: 'Science', label: 'Science' },
  { value: 'Other', label: 'Other' },
];

/**
 * Common qualification options
 */
export const QUALIFICATION_OPTIONS = [
  { value: '', label: 'Select Qualification' },
  { value: '10th Grade', label: '10th Grade' },
  { value: '12th Grade', label: '12th Grade' },
  { value: 'Diploma', label: 'Diploma' },
  { value: 'Bachelor\'s Degree', label: 'Bachelor\'s Degree' },
  { value: 'Master\'s Degree', label: 'Master\'s Degree' },
  { value: 'PhD', label: 'PhD' },
  { value: 'Other', label: 'Other' },
];

/**
 * Form field configurations
 */
export const FORM_FIELDS = {
  first_name: {
    label: 'First Name',
    type: 'text',
    placeholder: 'Enter your first name',
    required: true,
    maxLength: 50,
  },
  last_name: {
    label: 'Last Name',
    type: 'text',
    placeholder: 'Enter your last name',
    required: true,
    maxLength: 50,
  },
  date_of_birth: {
    label: 'Date of Birth',
    type: 'date',
    placeholder: '',
    required: true,
  },
  gender: {
    label: 'Gender',
    type: 'select',
    placeholder: 'Select your gender',
    required: true,
    options: GENDER_OPTIONS,
  },
  email: {
    label: 'Email',
    type: 'email',
    placeholder: 'Enter your email address',
    required: true,
    maxLength: 255,
  },
  phone_number: {
    label: 'Phone Number',
    type: 'tel',
    placeholder: 'Enter your phone number',
    required: true,
    maxLength: 15,
  },
  address: {
    label: 'Address',
    type: 'textarea',
    placeholder: 'Enter your complete address',
    required: true,
    maxLength: 500,
    rows: 3,
  },
  course_applied_for: {
    label: 'Course Applied For',
    type: 'select',
    placeholder: 'Select the course you want to apply for',
    required: true,
    options: COURSE_OPTIONS,
    allowCustom: true,
  },
  qualification: {
    label: 'Qualification',
    type: 'select',
    placeholder: 'Select your highest qualification',
    required: true,
    options: QUALIFICATION_OPTIONS,
    allowCustom: true,
  },
  marks_percentage: {
    label: 'Marks Percentage',
    type: 'number',
    placeholder: 'Enter your percentage (0-100)',
    required: true,
    min: 0,
    max: 100,
    step: 0.01,
  },
  application_date: {
    label: 'Application Date',
    type: 'date',
    placeholder: '',
    required: false,
    defaultValue: () => new Date().toISOString().split('T')[0],
  },
};

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  STUDENTS: '/api/students',
  STUDENT_STATS: '/api/students/stats',
  HEALTH: '/api/health',
  LEGACY: {
    CREATE: '/api/postData',
    FETCH_ALL: '/api/fetchData',
    FETCH_BY_ID: '/api/fetchById',
  },
};

/**
 * UI Constants
 */
export const UI_CONSTANTS = {
  COLORS: {
    PRIMARY: 'blue',
    SUCCESS: 'green',
    ERROR: 'red',
    WARNING: 'yellow',
    INFO: 'blue',
  },
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
  },
  ANIMATION_DURATION: {
    FAST: '150ms',
    NORMAL: '300ms',
    SLOW: '500ms',
  },
};

/**
 * Toast notification types
 */
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
};

/**
 * Loading states
 */
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
};

/**
 * Default form data
 */
export const DEFAULT_STUDENT_FORM = {
  first_name: '',
  last_name: '',
  date_of_birth: '',
  gender: '',
  email: '',
  phone_number: '',
  address: '',
  course_applied_for: '',
  qualification: '',
  marks_percentage: '',
  application_date: '',
};

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  VALIDATION_ERROR: 'Please correct the errors in the form.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
  STUDENT_NOT_FOUND: 'Student not found.',
  EMAIL_EXISTS: 'Email already exists. Please use a different email.',
};
