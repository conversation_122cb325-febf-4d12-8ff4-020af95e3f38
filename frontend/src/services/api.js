/**
 * API Service Layer
 * Centralized API calls with proper error handling and response formatting
 */

const API_BASE_URL = '/api';

/**
 * Custom error class for API errors
 */
class APIError extends Error {
  constructor(message, status, details = null) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.details = details;
  }
}

/**
 * Generic API request handler with error handling
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new APIError(
        data.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        data.details || null
      );
    }

    return data;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }
    
    // Network or other errors
    throw new APIError(
      error.message || 'Network error occurred',
      0,
      null
    );
  }
}

/**
 * Student API operations
 */
export const studentAPI = {
  /**
   * Create a new student
   */
  async create(studentData) {
    return apiRequest('/students', {
      method: 'POST',
      body: JSON.stringify(studentData),
    });
  },

  /**
   * Get all students
   */
  async getAll() {
    return apiRequest('/students');
  },

  /**
   * Get student by ID
   */
  async getById(id) {
    return apiRequest(`/students/${id}`);
  },

  /**
   * Update student by ID
   */
  async update(id, updateData) {
    return apiRequest(`/students/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  /**
   * Delete student by ID
   */
  async delete(id) {
    return apiRequest(`/students/${id}`, {
      method: 'DELETE',
    });
  },

  /**
   * Get student statistics
   */
  async getStats() {
    return apiRequest('/students/stats');
  },

  // Legacy endpoints for backward compatibility
  legacy: {
    async create(studentData) {
      return apiRequest('/postData', {
        method: 'POST',
        body: JSON.stringify(studentData),
      });
    },

    async getAll() {
      return apiRequest('/fetchData');
    },

    async getById(id) {
      return apiRequest(`/fetchById/${id}`);
    },
  },
};

/**
 * System API operations
 */
export const systemAPI = {
  /**
   * Health check
   */
  async health() {
    return apiRequest('/health');
  },

  /**
   * Get API info
   */
  async info() {
    return fetch('/').then(res => res.json());
  },
};

export { APIError };
