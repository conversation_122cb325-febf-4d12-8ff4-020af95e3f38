import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import StudentList from "../components/StudentList";
import StudentModal from "../components/StudentModal";
import Button from "../components/ui/Button";
import Card, {
  CardHeader,
  CardTitle,
  CardContent,
} from "../components/ui/Card";
import {
  useStudents,
  useStudentStats,
  STUDENT_QUERY_KEYS,
} from "../hooks/useStudents";

/**
 * Students Page Component
 * Main page for viewing and managing all student applications
 */
const Students = () => {
  const queryClient = useQueryClient();
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [modalMode, setModalMode] = useState("view");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: students = [], isLoading } = useStudents();
  const { data: backendStats, isLoading: statsLoading } = useStudentStats();

  // Auto-refresh data when component mounts
  useEffect(() => {
    queryClient.refetchQueries({ queryKey: STUDENT_QUERY_KEYS.list() });
    queryClient.refetchQueries({ queryKey: STUDENT_QUERY_KEYS.stats() });
  }, [queryClient]);

  // Also refresh when window gains focus
  useEffect(() => {
    const handleFocus = () => {
      queryClient.refetchQueries({ queryKey: STUDENT_QUERY_KEYS.list() });
      queryClient.refetchQueries({ queryKey: STUDENT_QUERY_KEYS.stats() });
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [queryClient]);

  const handleStudentView = (student) => {
    setSelectedStudent(student);
    setModalMode("view");
    setIsModalOpen(true);
  };

  const handleStudentEdit = (student) => {
    setSelectedStudent(student);
    setModalMode("edit");
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedStudent(null);
  };

  const getStats = () => {
    if (!students.length) return { total: 0, avgMarks: 0, topCourse: "N/A" };

    const total = students.length;
    // Use backend calculated average marks if available, otherwise fallback to local calculation
    const avgMarks =
      backendStats?.averageMarks ??
      students.reduce((sum, s) => sum + (s.marks_percentage || 0), 0) / total;

    const courseCounts = students.reduce((acc, s) => {
      acc[s.course_applied_for] = (acc[s.course_applied_for] || 0) + 1;
      return acc;
    }, {});

    const topCourse =
      Object.entries(courseCounts).reduce((a, b) =>
        courseCounts[a[0]] > courseCounts[b[0]] ? a : b
      )?.[0] || "N/A";

    return {
      total,
      avgMarks: typeof avgMarks === "number" ? avgMarks.toFixed(1) : "0.0",
      topCourse,
    };
  };

  const stats = getStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 slide-in-up">
          <h1 className="text-4xl font-bold gradient-text mb-4">
            Student Applications
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            View, manage, and track all student applications in one place
          </p>
          <div className="mt-6 flex justify-center">
            <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 slide-in-left">
          <Card className="modern-card hover-lift text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {stats.total}
              </div>
              <div className="text-gray-600">Total Applications</div>
            </CardContent>
          </Card>

          <Card className="modern-card hover-lift text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {stats.avgMarks}%
              </div>
              <div className="text-gray-600">Average Marks</div>
            </CardContent>
          </Card>

          <Card className="modern-card hover-lift text-center">
            <CardContent className="p-6">
              <div className="text-lg font-bold text-purple-600 mb-2 truncate">
                {stats.topCourse}
              </div>
              <div className="text-gray-600">Most Popular Course</div>
            </CardContent>
          </Card>
        </div>

        {/* Action Bar */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-semibold text-gray-900">
              All Applications
            </h2>
            {!isLoading && (
              <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                {students.length} total
              </span>
            )}
          </div>

          <Link to="/form">
            <Button
              variant="primary"
              className="btn-modern gradient-bg-primary"
            >
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Add New Student
            </Button>
          </Link>
        </div>

        {/* Student List */}
        <div className="slide-in-right">
          <StudentList
            onStudentView={handleStudentView}
            onStudentEdit={handleStudentEdit}
          />
        </div>

        {/* Student Modal */}
        <StudentModal
          student={selectedStudent}
          isOpen={isModalOpen}
          onClose={handleModalClose}
          mode={modalMode}
        />
      </div>
    </div>
  );
};

export default Students;
