import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const Form = () => {
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    date_of_birth: "",
    gender: "",
    email: "",
    phone_number: "",
    address: "",
    course_applied_for: "",
    qualification: "",
    marks_percentage: "",
    application_date: "",
  });

  const { mutate: form } = useMutation({
    mutationFn: async ({
      first_name,
      last_name,
      date_of_birth,
      gender,
      email,
      phone_number,
      address,
      course_applied_for,
      qualification,
      marks_percentage,
      application_date,
    }) => {
      try {
        const res = await fetch("/api/postData", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            first_name,
            last_name,
            date_of_birth,
            gender,
            email,
            phone_number,
            address,
            course_applied_for,
            qualification,
            marks_percentage,
            application_date,
          }),
        });
        const data = await res.json();
        console.log("data", data);

        if (!res.ok) {
          throw new Error(data.message || "Login failed");
        }
        return data;
      } catch (error) {
        throw new Error(error.message || "Sometthing went wrong")
      }
    },
    onSuccess: () => {
      toast.success("Your form has been successfully submitted");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    form(formData);
  };

  const handleOnChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-2xl shadow-2xl w-full ">
        <h2 className="text-2xl font-bold text-center text-black mb-6">
          Student Registration
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* First Name */}
          <div>
            <label className="block font-semibold text-black mb-1">
              First Name
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleOnChange}
              placeholder="Enter your first name"
            />
          </div>

          {/* Last Name */}
          <div>
            <label className="block font-semibold text-black mb-1">
              Last Name
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleOnChange}
              placeholder="Enter your last name"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Date of Birth
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="date"
              name="date_of_birth"
              value={formData.date_of_birth}
              onChange={handleOnChange}
              placeholder="Enter your date of birth"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Gender
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="gender"
              value={formData.gender}
              onChange={handleOnChange}
              placeholder="Enter your gender"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">Email</label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="email"
              value={formData.email}
              onChange={handleOnChange}
              placeholder="Enter your email"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Phone_number
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="number"
              name="phone_number"
              value={formData.phone_number}
              onChange={handleOnChange}
              placeholder="Enter your phone number"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Address
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="address"
              value={formData.address}
              onChange={handleOnChange}
              placeholder="Enter your address"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Course_Applied_For
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="course_applied_for"
              value={formData.course_applied_for}
              onChange={handleOnChange}
              placeholder="Enter your course name"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Qualification
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="text"
              name="qualification"
              value={formData.qualification}
              onChange={handleOnChange}
              placeholder="Enter your qualification"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Percentage
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="decimal"
              name="marks_percentage"
              value={formData.marks_percentage}
              onChange={handleOnChange}
              placeholder="Enter your percentage"
            />
          </div>
          <div>
            <label className="block font-semibold text-black mb-1">
              Application_Date
            </label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-black focus:outline-none focus:ring-2 focus:ring-blue-500"
              type="date"
              name="application_date"
              value={formData.application_date}
              onChange={handleOnChange}
              placeholder="Enter your application date"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Form;
