import { useState, useCallback } from "react";
import { useCreateStudent } from "../hooks/useStudents";
import { validateForm, validateField } from "../utils/validation";
import { DEFAULT_STUDENT_FORM, FORM_FIELDS } from "../utils/constants";
import Input, { Textarea, Select } from "../components/ui/Input";
import Button from "../components/ui/Button";
import Card, {
  CardHeader,
  CardTitle,
  CardContent,
} from "../components/ui/Card";

const Form = () => {
  const [formData, setFormData] = useState(DEFAULT_STUDENT_FORM);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  const {
    mutate: createStudent,
    isPending,
    isSuccess,
  } = useCreateStudent({
    onSuccess: () => {
      // Reset form on success with a slight delay for better UX
      setTimeout(() => {
        setFormData(DEFAULT_STUDENT_FORM);
        setErrors({});
        setTouched({});
      }, 1000);
    },
  });

  // Handle form submission
  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();

      // Validate entire form
      const { errors: validationErrors, hasErrors } = validateForm(formData);

      if (hasErrors) {
        setErrors(validationErrors);
        // Mark all fields as touched to show errors
        const allTouched = Object.keys(formData).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {});
        setTouched(allTouched);
        return;
      }

      // Clear errors and submit
      setErrors({});
      createStudent(formData);
    },
    [formData, createStudent]
  );

  // Handle input changes
  const handleChange = useCallback(
    (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));

      // Clear field error when user starts typing
      if (errors[name]) {
        setErrors((prev) => ({ ...prev, [name]: null }));
      }
    },
    [errors]
  );

  // Handle field blur for validation
  const handleBlur = useCallback((e) => {
    const { name, value } = e.target;
    setTouched((prev) => ({ ...prev, [name]: true }));

    // Validate field on blur
    const fieldErrors = validateField(name, value);
    setErrors((prev) => ({
      ...prev,
      [name]: fieldErrors.length > 0 ? fieldErrors : null,
    }));
  }, []);

  // Render form field based on configuration
  const renderField = (fieldName, config) => {
    const commonProps = {
      name: fieldName,
      value: formData[fieldName],
      onChange: handleChange,
      onBlur: handleBlur,
      error: touched[fieldName] ? errors[fieldName] : null,
      required: config.required,
      placeholder: config.placeholder,
      disabled: isPending,
    };

    switch (config.type) {
      case "textarea":
        return (
          <Textarea
            key={fieldName}
            label={config.label}
            rows={config.rows}
            maxLength={config.maxLength}
            {...commonProps}
          />
        );

      case "select":
        return (
          <Select
            key={fieldName}
            label={config.label}
            options={config.options}
            {...commonProps}
          />
        );

      default:
        return (
          <Input
            key={fieldName}
            label={config.label}
            type={config.type}
            min={config.min}
            max={config.max}
            step={config.step}
            maxLength={config.maxLength}
            {...commonProps}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <Card
          className={`modern-card shadow-2xl transition-all duration-500 slide-in-up ${
            isSuccess ? "ring-4 ring-green-300 shadow-green-200 pulse-glow" : ""
          }`}
        >
          <CardHeader>
            <CardTitle className="text-center gradient-text text-3xl">
              Student Registration
            </CardTitle>
            <p className="text-center text-gray-600 mt-3 text-lg">
              Please fill in all required fields to complete your application
            </p>
            <div className="mt-4 flex justify-center">
              <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            </div>
            {isSuccess && (
              <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg scale-in">
                <p className="text-center text-green-700 font-semibold flex items-center justify-center">
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Registration successful! Form will reset shortly...
                </p>
              </div>
            )}
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Render form fields dynamically */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(FORM_FIELDS).map(([fieldName, config]) => {
                  // Skip application_date as it's optional and auto-filled
                  if (fieldName === "application_date") return null;

                  // Full width fields
                  const fullWidthFields = ["address", "course_applied_for"];
                  const isFullWidth = fullWidthFields.includes(fieldName);

                  return (
                    <div
                      key={fieldName}
                      className={isFullWidth ? "md:col-span-2" : ""}
                    >
                      {renderField(fieldName, config)}
                    </div>
                  );
                })}
              </div>

              {/* Application Date (Optional) */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Optional Information
                </h3>
                {renderField("application_date", FORM_FIELDS.application_date)}
                <p className="text-sm text-gray-500 mt-1">
                  Leave blank to use today's date
                </p>
              </div>

              {/* Submit Button */}
              <div className="flex justify-center pt-8">
                <Button
                  type="submit"
                  size="lg"
                  loading={isPending}
                  disabled={isPending}
                  className="w-full md:w-auto min-w-[250px] btn-modern gradient-bg-primary hover:shadow-2xl"
                >
                  {isPending ? "Submitting..." : "Submit Application"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Form;
