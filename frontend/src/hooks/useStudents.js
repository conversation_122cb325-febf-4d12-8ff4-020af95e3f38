import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { studentAPI } from '../services/api';
import { toast } from 'react-hot-toast';

/**
 * Custom hook for student operations
 * Provides centralized state management for student data
 */

// Query keys for React Query
export const STUDENT_QUERY_KEYS = {
  all: ['students'],
  list: () => [...STUDENT_QUERY_KEYS.all, 'list'],
  detail: (id) => [...STUDENT_QUERY_KEYS.all, 'detail', id],
  stats: () => [...STUDENT_QUERY_KEYS.all, 'stats'],
};

/**
 * Hook to fetch all students
 */
export const useStudents = (options = {}) => {
  return useQuery({
    queryKey: STUDENT_QUERY_KEYS.list(),
    queryFn: studentAPI.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook to fetch a single student by ID
 */
export const useStudent = (id, options = {}) => {
  return useQuery({
    queryKey: STUDENT_QUERY_KEYS.detail(id),
    queryFn: () => studentAPI.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook to fetch student statistics
 */
export const useStudentStats = (options = {}) => {
  return useQuery({
    queryKey: STUDENT_QUERY_KEYS.stats(),
    queryFn: studentAPI.getStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook to create a new student
 */
export const useCreateStudent = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: studentAPI.create,
    onSuccess: (data) => {
      // Invalidate and refetch students list
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.stats() });
      
      // Show success message
      toast.success('Student created successfully!');
      
      // Call custom success handler if provided
      if (options.onSuccess) {
        options.onSuccess(data);
      }
    },
    onError: (error) => {
      // Show error message
      toast.error(error.message || 'Failed to create student');
      
      // Call custom error handler if provided
      if (options.onError) {
        options.onError(error);
      }
    },
    ...options,
  });
};

/**
 * Hook to update a student
 */
export const useUpdateStudent = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => studentAPI.update(id, data),
    onSuccess: (data, variables) => {
      // Update the specific student in cache
      queryClient.setQueryData(
        STUDENT_QUERY_KEYS.detail(variables.id),
        data
      );
      
      // Invalidate students list to ensure consistency
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.stats() });
      
      // Show success message
      toast.success('Student updated successfully!');
      
      // Call custom success handler if provided
      if (options.onSuccess) {
        options.onSuccess(data, variables);
      }
    },
    onError: (error) => {
      // Show error message
      toast.error(error.message || 'Failed to update student');
      
      // Call custom error handler if provided
      if (options.onError) {
        options.onError(error);
      }
    },
    ...options,
  });
};

/**
 * Hook to delete a student
 */
export const useDeleteStudent = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: studentAPI.delete,
    onSuccess: (data, studentId) => {
      // Remove student from cache
      queryClient.removeQueries({ queryKey: STUDENT_QUERY_KEYS.detail(studentId) });
      
      // Invalidate students list
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.stats() });
      
      // Show success message
      toast.success('Student deleted successfully!');
      
      // Call custom success handler if provided
      if (options.onSuccess) {
        options.onSuccess(data, studentId);
      }
    },
    onError: (error) => {
      // Show error message
      toast.error(error.message || 'Failed to delete student');
      
      // Call custom error handler if provided
      if (options.onError) {
        options.onError(error);
      }
    },
    ...options,
  });
};

/**
 * Hook for legacy API operations (backward compatibility)
 */
export const useLegacyStudents = () => {
  const queryClient = useQueryClient();

  const createLegacy = useMutation({
    mutationFn: studentAPI.legacy.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: STUDENT_QUERY_KEYS.stats() });
      toast.success('Student created successfully!');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create student');
    },
  });

  const fetchAllLegacy = useQuery({
    queryKey: ['students', 'legacy'],
    queryFn: studentAPI.legacy.getAll,
    staleTime: 5 * 60 * 1000,
  });

  const fetchByIdLegacy = (id) => useQuery({
    queryKey: ['students', 'legacy', id],
    queryFn: () => studentAPI.legacy.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });

  return {
    createLegacy,
    fetchAllLegacy,
    fetchByIdLegacy,
  };
};

/**
 * Hook to prefetch student data
 * Useful for optimistic loading
 */
export const usePrefetchStudent = () => {
  const queryClient = useQueryClient();

  const prefetchStudent = (id) => {
    queryClient.prefetchQuery({
      queryKey: STUDENT_QUERY_KEYS.detail(id),
      queryFn: () => studentAPI.getById(id),
      staleTime: 5 * 60 * 1000,
    });
  };

  const prefetchStudents = () => {
    queryClient.prefetchQuery({
      queryKey: STUDENT_QUERY_KEYS.list(),
      queryFn: studentAPI.getAll,
      staleTime: 5 * 60 * 1000,
    });
  };

  return {
    prefetchStudent,
    prefetchStudents,
  };
};
