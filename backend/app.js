const express = require("express");
const cors = require("cors");
const database = require("./src/config/database");
const environment = require("./src/config/environment");
const studentRoutes = require("./src/routes/studentRoutes");
const {
  errorHand<PERSON>,
  notFoundHandler,
} = require("./src/middleware/errorHandler");

const app = express();

// Get server configuration
const serverConfig = environment.getServerConfig();

// Middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));
app.use(
  cors({
    origin: serverConfig.corsOrigin,
    credentials: true,
  })
);

// Request logging middleware (development only)
if (environment.isDevelopment()) {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
  });
}

// Initialize database connection
async function initializeDatabase() {
  try {
    await database.initialize();
    console.log("🚀 Database initialized successfully");
  } catch (error) {
    console.error("❌ Failed to initialize database:", error.message);
    process.exit(1);
  }
}

// Routes
app.use("/api", studentRoutes);

// Root endpoint
app.get("/", (req, res) => {
  res.json({
    message: "College Project Backend API",
    version: "1.0.0",
    endpoints: {
      health: "/health",
      students: "/api/students",
      legacy: {
        create: "/api/postData",
        fetchAll: "/api/fetchData",
        fetchById: "/api/fetchById/:id",
      },
    },
  });
});

// Health check endpoint
app.get("/health", (req, res) => {
  const dbStatus = database.isConnectedToDatabase();
  const poolStats = database.getPoolStats();

  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    database: {
      connected: dbStatus,
      poolStats: poolStats,
    },
    environment: serverConfig.nodeEnv,
  });
});

// Error handling middleware (must be last)
app.use(notFoundHandler);
app.use(errorHandler);

// Graceful shutdown handling
process.on("SIGTERM", async () => {
  console.log("🛑 SIGTERM received, shutting down gracefully...");
  await database.close();
  process.exit(0);
});

process.on("SIGINT", async () => {
  console.log("🛑 SIGINT received, shutting down gracefully...");
  await database.close();
  process.exit(0);
});

// Start server
async function startServer() {
  try {
    // Initialize database first
    await initializeDatabase();

    // Start the server
    app.listen(serverConfig.port, () => {
      console.log(`🚀 Server is running on port ${serverConfig.port}`);
      console.log(`🌍 Environment: ${serverConfig.nodeEnv}`);
      console.log(`🔗 CORS Origin: ${serverConfig.corsOrigin}`);
      console.log(
        `📊 Health check: http://localhost:${serverConfig.port}/health`
      );
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error.message);
    process.exit(1);
  }
}

// Start the application
startServer();
