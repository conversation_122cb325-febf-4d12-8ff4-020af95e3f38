const express = require("express");
const StudentController = require("../controllers/studentController");
const {
  validateStudentCreation,
  validateStudentUpdate,
  validateStudentId,
} = require("../middleware/validation");

const router = express.Router();

/**
 * Student Routes
 * RESTful API endpoints for student operations
 */

// Modern RESTful routes
router.post(
  "/students",
  validateStudentCreation,
  StudentController.createStudent
);
router.get("/students", StudentController.getAllStudents);
router.get("/students/stats", StudentController.getStudentStats); // Must be before /:id route
router.get(
  "/students/:id",
  validateStudentId,
  StudentController.getStudentById
);
router.put(
  "/students/:id",
  validateStudentId,
  validateStudentUpdate,
  StudentController.updateStudent
);
router.delete(
  "/students/:id",
  validateStudentId,
  StudentController.deleteStudent
);

// Legacy routes for backward compatibility (with validation)
router.post(
  "/postData",
  validateStudentCreation,
  StudentController.legacyCreateStudent
);
router.get("/fetchData", StudentController.legacyGetAllStudents);
router.get(
  "/fetchById/:id",
  validateStudentId,
  StudentController.legacyGetStudentById
);

module.exports = router;
