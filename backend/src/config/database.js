const { Pool } = require('pg');
const environment = require('./environment');

/**
 * Database configuration and connection management
 * Handles PostgreSQL connection pooling and provides database utilities
 */
class DatabaseConfig {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }

  /**
   * Initialize database connection pool
   * @returns {Promise<Pool>} PostgreSQL connection pool
   */
  async initialize() {
    try {
      const dbConfig = environment.getDatabaseConfig();
      const poolConfig = environment.getPoolConfig();

      // Combine database and pool configurations
      const config = {
        ...dbConfig,
        ...poolConfig
      };

      this.pool = new Pool(config);

      // Set up event handlers
      this.setupEventHandlers();

      // Test the connection
      await this.testConnection();

      console.log('✅ Database connection pool initialized successfully');
      this.isConnected = true;
      
      return this.pool;
    } catch (error) {
      console.error('❌ Failed to initialize database connection:', error.message);
      throw error;
    }
  }

  /**
   * Set up event handlers for the connection pool
   */
  setupEventHandlers() {
    this.pool.on('connect', (client) => {
      if (environment.isDevelopment()) {
        console.log('🔗 New database client connected');
      }
    });

    this.pool.on('error', (err, client) => {
      console.error('❌ Unexpected error on idle database client:', err);
    });

    this.pool.on('remove', (client) => {
      if (environment.isDevelopment()) {
        console.log('🔌 Database client removed from pool');
      }
    });
  }

  /**
   * Test database connection
   * @returns {Promise<void>}
   */
  async testConnection() {
    try {
      const client = await this.pool.connect();
      const result = await client.query('SELECT NOW() as current_time, version() as version');
      client.release();
      
      console.log('🔍 Database connection test successful');
      console.log(`📅 Server time: ${result.rows[0].current_time}`);
      
      if (environment.isDevelopment()) {
        console.log(`🗄️  Database version: ${result.rows[0].version.split(' ')[0]} ${result.rows[0].version.split(' ')[1]}`);
      }
    } catch (error) {
      throw new Error(`Database connection test failed: ${error.message}`);
    }
  }

  /**
   * Get database connection pool
   * @returns {Pool} PostgreSQL connection pool
   */
  getPool() {
    if (!this.pool) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.pool;
  }

  /**
   * Execute a query with automatic connection management
   * @param {string} text - SQL query text
   * @param {Array} params - Query parameters
   * @returns {Promise<Object>} Query result
   */
  async query(text, params = []) {
    if (!this.pool) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      
      if (environment.isDevelopment()) {
        console.log(`🔍 Query executed in ${duration}ms:`, {
          text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
          rowCount: result.rowCount
        });
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`❌ Query failed after ${duration}ms:`, {
        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Execute a transaction with automatic rollback on error
   * @param {Function} callback - Function that receives client and executes queries
   * @returns {Promise<any>} Transaction result
   */
  async transaction(callback) {
    if (!this.pool) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Check if database is connected
   * @returns {boolean} Connection status
   */
  isConnectedToDatabase() {
    return this.isConnected && this.pool && !this.pool.ended;
  }

  /**
   * Gracefully close database connections
   * @returns {Promise<void>}
   */
  async close() {
    if (this.pool) {
      console.log('🔌 Closing database connection pool...');
      await this.pool.end();
      this.isConnected = false;
      console.log('✅ Database connection pool closed');
    }
  }

  /**
   * Get connection pool statistics
   * @returns {Object} Pool statistics
   */
  getPoolStats() {
    if (!this.pool) {
      return { error: 'Database not initialized' };
    }

    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount
    };
  }
}

// Export singleton instance
module.exports = new DatabaseConfig();
