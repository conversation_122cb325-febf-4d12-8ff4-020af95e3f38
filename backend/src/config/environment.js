const dotenv = require('dotenv');

// Load environment variables from .env file
dotenv.config();

/**
 * Environment configuration with validation
 * Ensures all required environment variables are present and valid
 */
class EnvironmentConfig {
  constructor() {
    this.validateEnvironment();
  }

  /**
   * Validates that all required environment variables are present
   * @throws {Error} If any required environment variable is missing
   */
  validateEnvironment() {
    const requiredVars = [];
    
    // Check if DATABASE_URL is provided, or individual DB config
    if (!process.env.DATABASE_URL) {
      requiredVars.push(
        'DB_HOST',
        'DB_PORT', 
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD'
      );
    }

    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missingVars.join(', ')}\n` +
        'Please check your .env file or environment configuration.'
      );
    }

    // Validate PORT is a number
    if (process.env.PORT && isNaN(parseInt(process.env.PORT))) {
      throw new Error('PORT must be a valid number');
    }

    // Validate DB_PORT is a number
    if (process.env.DB_PORT && isNaN(parseInt(process.env.DB_PORT))) {
      throw new Error('DB_PORT must be a valid number');
    }
  }

  /**
   * Get database configuration
   * @returns {Object} Database configuration object
   */
  getDatabaseConfig() {
    if (process.env.DATABASE_URL) {
      return {
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
      };
    }

    return {
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT) || 5432,
      database: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      ssl: this.getBoolean(process.env.DB_SSL, true)
    };
  }

  /**
   * Get database pool configuration
   * @returns {Object} Pool configuration object
   */
  getPoolConfig() {
    return {
      min: parseInt(process.env.DB_POOL_MIN) || 2,
      max: parseInt(process.env.DB_POOL_MAX) || 10,
      idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 30000,
      connectionTimeoutMillis: parseInt(process.env.DB_POOL_CONNECTION_TIMEOUT) || 2000
    };
  }

  /**
   * Get server configuration
   * @returns {Object} Server configuration object
   */
  getServerConfig() {
    return {
      port: parseInt(process.env.PORT) || 3000,
      nodeEnv: process.env.NODE_ENV || 'development',
      corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:5000'
    };
  }

  /**
   * Get logging configuration
   * @returns {Object} Logging configuration object
   */
  getLoggingConfig() {
    return {
      level: process.env.LOG_LEVEL || 'info'
    };
  }

  /**
   * Helper method to parse boolean environment variables
   * @param {string} value - Environment variable value
   * @param {boolean} defaultValue - Default value if not provided
   * @returns {boolean} Parsed boolean value
   */
  getBoolean(value, defaultValue = false) {
    if (value === undefined || value === null) {
      return defaultValue;
    }
    return value.toLowerCase() === 'true' || value === '1';
  }

  /**
   * Check if running in production environment
   * @returns {boolean} True if in production
   */
  isProduction() {
    return process.env.NODE_ENV === 'production';
  }

  /**
   * Check if running in development environment
   * @returns {boolean} True if in development
   */
  isDevelopment() {
    return process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
  }
}

// Export singleton instance
module.exports = new EnvironmentConfig();
