const Student = require('../models/Student');

/**
 * Student Service
 * Contains business logic for student operations
 */
class StudentService {
  /**
   * Create a new student
   * @param {Object} studentData - Student information
   * @returns {Promise<Object>} Created student record
   */
  static async createStudent(studentData) {
    // Check if email already exists
    const emailExists = await Student.emailExists(studentData.email);
    if (emailExists) {
      throw new Error('Email already exists');
    }

    // Validate required fields
    const requiredFields = [
      'first_name', 'last_name', 'date_of_birth', 'gender', 
      'email', 'phone_number', 'address', 'course_applied_for', 
      'qualification', 'marks_percentage'
    ];

    for (const field of requiredFields) {
      if (!studentData[field]) {
        throw new Error(`${field} is required`);
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(studentData.email)) {
      throw new Error('Invalid email format');
    }

    // Validate marks percentage
    const marks = parseFloat(studentData.marks_percentage);
    if (isNaN(marks) || marks < 0 || marks > 100) {
      throw new Error('Marks percentage must be between 0 and 100');
    }

    // Set application_date if not provided
    if (!studentData.application_date) {
      studentData.application_date = new Date().toISOString().split('T')[0];
    }

    return await Student.create(studentData);
  }

  /**
   * Get all students
   * @returns {Promise<Array>} Array of student records
   */
  static async getAllStudents() {
    return await Student.findAll();
  }

  /**
   * Get student by ID
   * @param {number} id - Student ID
   * @returns {Promise<Object>} Student record
   */
  static async getStudentById(id) {
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid student ID');
    }

    const student = await Student.findById(parseInt(id));
    if (!student) {
      throw new Error('Student not found');
    }

    return student;
  }

  /**
   * Update student by ID
   * @param {number} id - Student ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated student record
   */
  static async updateStudent(id, updateData) {
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid student ID');
    }

    // Check if student exists
    const existingStudent = await Student.findById(parseInt(id));
    if (!existingStudent) {
      throw new Error('Student not found');
    }

    // Check email uniqueness if email is being updated
    if (updateData.email && updateData.email !== existingStudent.email) {
      const emailExists = await Student.emailExists(updateData.email, parseInt(id));
      if (emailExists) {
        throw new Error('Email already exists');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updateData.email)) {
        throw new Error('Invalid email format');
      }
    }

    // Validate marks percentage if provided
    if (updateData.marks_percentage !== undefined) {
      const marks = parseFloat(updateData.marks_percentage);
      if (isNaN(marks) || marks < 0 || marks > 100) {
        throw new Error('Marks percentage must be between 0 and 100');
      }
    }

    return await Student.updateById(parseInt(id), updateData);
  }

  /**
   * Delete student by ID
   * @param {number} id - Student ID
   * @returns {Promise<boolean>} True if deleted successfully
   */
  static async deleteStudent(id) {
    if (!id || isNaN(parseInt(id))) {
      throw new Error('Invalid student ID');
    }

    const deleted = await Student.deleteById(parseInt(id));
    if (!deleted) {
      throw new Error('Student not found');
    }

    return true;
  }

  /**
   * Get student statistics
   * @returns {Promise<Object>} Student statistics
   */
  static async getStudentStats() {
    const totalStudents = await Student.getCount();
    const allStudents = await Student.findAll();
    
    // Calculate average marks
    let totalMarks = 0;
    let validMarks = 0;
    
    allStudents.forEach(student => {
      const marks = parseFloat(student.marks_percentage);
      if (!isNaN(marks)) {
        totalMarks += marks;
        validMarks++;
      }
    });

    const averageMarks = validMarks > 0 ? (totalMarks / validMarks).toFixed(2) : 0;

    return {
      totalStudents,
      averageMarks: parseFloat(averageMarks)
    };
  }
}

module.exports = StudentService;
