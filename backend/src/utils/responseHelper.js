/**
 * Response Helper Utilities
 * Standardized response formatting
 */

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code
 */
function sendSuccess(res, data, message = 'Success', statusCode = 200) {
  res.status(statusCode).json({
    success: true,
    message,
    data
  });
}

/**
 * Send error response
 * @param {Object} res - Express response object
 * @param {string} error - Error type
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {any} details - Additional error details
 */
function sendError(res, error, message, statusCode = 500, details = null) {
  const response = {
    success: false,
    error,
    message
  };

  if (details) {
    response.details = details;
  }

  res.status(statusCode).json(response);
}

/**
 * Send validation error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {Array} errors - Validation errors array
 */
function sendValidationError(res, message, errors = []) {
  sendError(res, 'Validation Error', message, 400, errors);
}

/**
 * Send not found error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
function sendNotFound(res, message = 'Resource not found') {
  sendError(res, 'Not Found', message, 404);
}

/**
 * Send server error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
function sendServerError(res, message = 'Internal server error') {
  sendError(res, 'Server Error', message, 500);
}

/**
 * Send created response
 * @param {Object} res - Express response object
 * @param {any} data - Created resource data
 * @param {string} message - Success message
 */
function sendCreated(res, data, message = 'Resource created successfully') {
  sendSuccess(res, data, message, 201);
}

/**
 * Send no content response
 * @param {Object} res - Express response object
 */
function sendNoContent(res) {
  res.status(204).send();
}

module.exports = {
  sendSuccess,
  sendError,
  sendValidationError,
  sendNotFound,
  sendServerError,
  sendCreated,
  sendNoContent
};
