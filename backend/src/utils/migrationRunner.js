const fs = require("fs").promises;
const path = require("path");
const database = require("../config/database");

/**
 * Simple Migration Runner
 * Just runs the student table migration
 */
class MigrationRunner {
  constructor() {
    this.migrationsPath = path.join(__dirname, "../../migrations");
  }

  /**
   * Run the student table migration
   */
  async runMigrations() {
    try {
      console.log("🚀 Setting up student table...");

      // Read the migration file
      const migrationFile = path.join(
        this.migrationsPath,
        "001_create_students_table.sql"
      );
      const migrationSQL = await fs.readFile(migrationFile, "utf8");

      // Execute the migration
      await database.query(migrationSQL);

      console.log("✅ Student table created successfully!");
    } catch (error) {
      console.error("❌ Migration failed:", error.message);
      throw error;
    }
  }

  /**
   * Check if student table exists
   */
  async checkTableExists() {
    try {
      const result = await database.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'student'
        );
      `);
      return result.rows[0].exists;
    } catch (error) {
      return false;
    }
  }
}

module.exports = new MigrationRunner();
