const StudentService = require('../services/studentService');

/**
 * Student Controller
 * Handles HTTP requests and responses for student operations
 */
class StudentController {
  /**
   * Create a new student
   * POST /students
   */
  static async createStudent(req, res) {
    try {
      const student = await StudentService.createStudent(req.body);
      res.status(201).json(student);
    } catch (error) {
      console.error('Create student error:', error.message);
      
      if (error.message.includes('already exists') || 
          error.message.includes('required') ||
          error.message.includes('Invalid email') ||
          error.message.includes('Marks percentage')) {
        return res.status(400).json({
          error: 'Validation Error',
          message: error.message
        });
      }
      
      res.status(500).json({
        error: 'Failed to create student record',
        message: error.message
      });
    }
  }

  /**
   * Get all students
   * GET /students
   */
  static async getAllStudents(req, res) {
    try {
      const students = await StudentService.getAllStudents();
      res.status(200).json(students);
    } catch (error) {
      console.error('Get all students error:', error.message);
      res.status(500).json({
        error: 'Failed to fetch student records',
        message: error.message
      });
    }
  }

  /**
   * Get student by ID
   * GET /students/:id
   */
  static async getStudentById(req, res) {
    try {
      const student = await StudentService.getStudentById(req.params.id);
      res.status(200).json(student);
    } catch (error) {
      console.error('Get student by ID error:', error.message);
      
      if (error.message.includes('Invalid student ID') || 
          error.message.includes('not found')) {
        return res.status(404).json({
          error: 'Student Not Found',
          message: error.message
        });
      }
      
      res.status(500).json({
        error: 'Failed to fetch student record',
        message: error.message
      });
    }
  }

  /**
   * Update student by ID
   * PUT /students/:id
   */
  static async updateStudent(req, res) {
    try {
      const student = await StudentService.updateStudent(req.params.id, req.body);
      res.status(200).json(student);
    } catch (error) {
      console.error('Update student error:', error.message);
      
      if (error.message.includes('Invalid student ID') || 
          error.message.includes('not found')) {
        return res.status(404).json({
          error: 'Student Not Found',
          message: error.message
        });
      }
      
      if (error.message.includes('already exists') ||
          error.message.includes('Invalid email') ||
          error.message.includes('Marks percentage')) {
        return res.status(400).json({
          error: 'Validation Error',
          message: error.message
        });
      }
      
      res.status(500).json({
        error: 'Failed to update student record',
        message: error.message
      });
    }
  }

  /**
   * Delete student by ID
   * DELETE /students/:id
   */
  static async deleteStudent(req, res) {
    try {
      await StudentService.deleteStudent(req.params.id);
      res.status(200).json({
        message: 'Student deleted successfully'
      });
    } catch (error) {
      console.error('Delete student error:', error.message);
      
      if (error.message.includes('Invalid student ID') || 
          error.message.includes('not found')) {
        return res.status(404).json({
          error: 'Student Not Found',
          message: error.message
        });
      }
      
      res.status(500).json({
        error: 'Failed to delete student record',
        message: error.message
      });
    }
  }

  /**
   * Get student statistics
   * GET /students/stats
   */
  static async getStudentStats(req, res) {
    try {
      const stats = await StudentService.getStudentStats();
      res.status(200).json(stats);
    } catch (error) {
      console.error('Get student stats error:', error.message);
      res.status(500).json({
        error: 'Failed to fetch student statistics',
        message: error.message
      });
    }
  }

  // Legacy endpoints for backward compatibility
  
  /**
   * Legacy create endpoint
   * POST /postData
   */
  static async legacyCreateStudent(req, res) {
    return StudentController.createStudent(req, res);
  }

  /**
   * Legacy fetch all endpoint
   * GET /fetchData
   */
  static async legacyGetAllStudents(req, res) {
    return StudentController.getAllStudents(req, res);
  }

  /**
   * Legacy fetch by ID endpoint
   * GET /fetchById/:id
   */
  static async legacyGetStudentById(req, res) {
    return StudentController.getStudentById(req, res);
  }
}

module.exports = StudentController;
