const database = require('../config/database');

/**
 * Student Model
 * Handles all database operations for students
 */
class Student {
  /**
   * Create a new student record
   * @param {Object} studentData - Student information
   * @returns {Promise<Object>} Created student record
   */
  static async create(studentData) {
    const {
      first_name,
      last_name,
      date_of_birth,
      gender,
      email,
      phone_number,
      address,
      course_applied_for,
      qualification,
      marks_percentage,
      application_date
    } = studentData;

    const query = `
      INSERT INTO student 
      (first_name, last_name, date_of_birth, gender, email, phone_number, address, course_applied_for, qualification, marks_percentage, application_date)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *;
    `;

    const values = [
      first_name,
      last_name,
      date_of_birth,
      gender,
      email,
      phone_number,
      address,
      course_applied_for,
      qualification,
      marks_percentage,
      application_date
    ];

    const result = await database.query(query, values);
    return result.rows[0];
  }

  /**
   * Get all students
   * @returns {Promise<Array>} Array of student records
   */
  static async findAll() {
    const query = "SELECT * FROM student ORDE<PERSON> BY created_at DESC";
    const result = await database.query(query);
    return result.rows;
  }

  /**
   * Find student by ID
   * @param {number} id - Student ID
   * @returns {Promise<Object|null>} Student record or null if not found
   */
  static async findById(id) {
    const query = "SELECT * FROM student WHERE student_id = $1";
    const result = await database.query(query, [id]);
    return result.rows[0] || null;
  }

  /**
   * Update student by ID
   * @param {number} id - Student ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated student record or null if not found
   */
  static async updateById(id, updateData) {
    const fields = [];
    const values = [];
    let paramCount = 1;

    // Build dynamic update query
    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined && key !== 'student_id') {
        fields.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    }

    if (fields.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(id);
    const query = `
      UPDATE student 
      SET ${fields.join(', ')} 
      WHERE student_id = $${paramCount}
      RETURNING *;
    `;

    const result = await database.query(query, values);
    return result.rows[0] || null;
  }

  /**
   * Delete student by ID
   * @param {number} id - Student ID
   * @returns {Promise<boolean>} True if deleted, false if not found
   */
  static async deleteById(id) {
    const query = "DELETE FROM student WHERE student_id = $1 RETURNING student_id";
    const result = await database.query(query, [id]);
    return result.rows.length > 0;
  }

  /**
   * Check if email already exists
   * @param {string} email - Email to check
   * @param {number} excludeId - Student ID to exclude from check (for updates)
   * @returns {Promise<boolean>} True if email exists
   */
  static async emailExists(email, excludeId = null) {
    let query = "SELECT student_id FROM student WHERE email = $1";
    const values = [email];

    if (excludeId) {
      query += " AND student_id != $2";
      values.push(excludeId);
    }

    const result = await database.query(query, values);
    return result.rows.length > 0;
  }

  /**
   * Get student count
   * @returns {Promise<number>} Total number of students
   */
  static async getCount() {
    const query = "SELECT COUNT(*) as count FROM student";
    const result = await database.query(query);
    return parseInt(result.rows[0].count);
  }
}

module.exports = Student;
