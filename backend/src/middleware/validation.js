/**
 * Validation Middleware
 * Request validation for student operations
 */

/**
 * Validate student creation data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateStudentCreation(req, res, next) {
  const {
    first_name,
    last_name,
    date_of_birth,
    gender,
    email,
    phone_number,
    address,
    course_applied_for,
    qualification,
    marks_percentage
  } = req.body;

  const errors = [];

  // Check required fields
  if (!first_name || first_name.trim().length === 0) {
    errors.push('First name is required');
  }
  if (!last_name || last_name.trim().length === 0) {
    errors.push('Last name is required');
  }
  if (!date_of_birth) {
    errors.push('Date of birth is required');
  }
  if (!gender) {
    errors.push('Gender is required');
  }
  if (!email || email.trim().length === 0) {
    errors.push('Email is required');
  }
  if (!phone_number || phone_number.trim().length === 0) {
    errors.push('Phone number is required');
  }
  if (!address || address.trim().length === 0) {
    errors.push('Address is required');
  }
  if (!course_applied_for || course_applied_for.trim().length === 0) {
    errors.push('Course applied for is required');
  }
  if (!qualification || qualification.trim().length === 0) {
    errors.push('Qualification is required');
  }
  if (marks_percentage === undefined || marks_percentage === null) {
    errors.push('Marks percentage is required');
  }

  // Validate email format
  if (email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      errors.push('Invalid email format');
    }
  }

  // Validate marks percentage
  if (marks_percentage !== undefined && marks_percentage !== null) {
    const marks = parseFloat(marks_percentage);
    if (isNaN(marks) || marks < 0 || marks > 100) {
      errors.push('Marks percentage must be between 0 and 100');
    }
  }

  // Validate date of birth
  if (date_of_birth) {
    const dob = new Date(date_of_birth);
    const today = new Date();
    const age = today.getFullYear() - dob.getFullYear();
    
    if (isNaN(dob.getTime())) {
      errors.push('Invalid date of birth format');
    } else if (age < 16 || age > 100) {
      errors.push('Age must be between 16 and 100 years');
    }
  }

  // Validate gender
  if (gender) {
    const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say'];
    if (!validGenders.includes(gender)) {
      errors.push('Gender must be one of: Male, Female, Other, Prefer not to say');
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid input data',
      details: errors
    });
  }

  next();
}

/**
 * Validate student update data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateStudentUpdate(req, res, next) {
  const {
    email,
    marks_percentage,
    date_of_birth,
    gender
  } = req.body;

  const errors = [];

  // Validate email format if provided
  if (email !== undefined) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      errors.push('Invalid email format');
    }
  }

  // Validate marks percentage if provided
  if (marks_percentage !== undefined && marks_percentage !== null) {
    const marks = parseFloat(marks_percentage);
    if (isNaN(marks) || marks < 0 || marks > 100) {
      errors.push('Marks percentage must be between 0 and 100');
    }
  }

  // Validate date of birth if provided
  if (date_of_birth !== undefined) {
    const dob = new Date(date_of_birth);
    const today = new Date();
    const age = today.getFullYear() - dob.getFullYear();
    
    if (isNaN(dob.getTime())) {
      errors.push('Invalid date of birth format');
    } else if (age < 16 || age > 100) {
      errors.push('Age must be between 16 and 100 years');
    }
  }

  // Validate gender if provided
  if (gender !== undefined) {
    const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say'];
    if (!validGenders.includes(gender)) {
      errors.push('Gender must be one of: Male, Female, Other, Prefer not to say');
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid input data',
      details: errors
    });
  }

  next();
}

/**
 * Validate student ID parameter
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateStudentId(req, res, next) {
  const id = parseInt(req.params.id);
  
  if (isNaN(id) || id <= 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid student ID. ID must be a positive number.'
    });
  }
  
  req.params.id = id; // Ensure it's a number
  next();
}

module.exports = {
  validateStudentCreation,
  validateStudentUpdate,
  validateStudentId
};
