# 🎓 College Project Backend

A professional **Student Registration System** backend built with Node.js, Express, and PostgreSQL. This system provides a complete API for managing student applications with modern architecture and comprehensive validation.

## 📋 Table of Contents

- [🎯 About the Project](#-about-the-project)
- [✨ Features](#-features)
- [🛠️ Technology Stack](#️-technology-stack)
- [📁 Project Structure](#-project-structure)
- [🚀 Getting Started](#-getting-started)
- [⚙️ Configuration](#️-configuration)
- [📡 API Documentation](#-api-documentation)
- [💾 Database Schema](#-database-schema)
- [🔧 Available Commands](#-available-commands)
- [🧪 Testing the API](#-testing-the-api)
- [🔒 Security Features](#-security-features)
- [🐛 Troubleshooting](#-troubleshooting)
- [📞 Support](#-support)

## 🎯 About the Project

This is a **Student Registration System** backend that allows educational institutions to:

- ✅ **Manage Student Applications**: Create, read, update, and delete student records
- ✅ **Validate Student Data**: Comprehensive validation for all student information
- ✅ **Track Application Statistics**: Get insights about student applications
- ✅ **Secure Data Storage**: PostgreSQL database with proper constraints
- ✅ **RESTful API**: Modern API design with backward compatibility

### 🎯 Use Cases:

- **Educational Institutions**: Manage student admissions and applications
- **Training Centers**: Handle course registrations
- **Online Learning Platforms**: Student enrollment management
- **Academic Projects**: Learning backend development with real-world examples

## ✨ Features

### 🏗️ **Professional Architecture**

- **MVC Pattern**: Models, Views, Controllers separation
- **Layered Architecture**: Routes → Controllers → Services → Models
- **Middleware Integration**: Validation, error handling, logging

### 🔒 **Security & Validation**

- **Input Validation**: Email format, age limits, required fields
- **SQL Injection Prevention**: Parameterized queries
- **Error Handling**: Centralized error management
- **Data Sanitization**: Clean and validate all inputs

### 📊 **Database Features**

- **PostgreSQL Integration**: Robust relational database
- **Connection Pooling**: Efficient database connections
- **Automatic Timestamps**: Track creation and updates
- **Data Integrity**: Unique constraints and validations

### 🚀 **API Features**

- **RESTful Design**: Standard HTTP methods and status codes
- **JSON Responses**: Structured data format
- **Legacy Support**: Backward compatibility
- **Health Monitoring**: System status endpoints

## 🛠️ Technology Stack

| Technology     | Purpose               | Version |
| -------------- | --------------------- | ------- |
| **Node.js**    | Runtime Environment   | 18+     |
| **Express.js** | Web Framework         | 4.x     |
| **PostgreSQL** | Database              | 13+     |
| **Neon**       | Database Hosting      | Cloud   |
| **CORS**       | Cross-Origin Requests | Latest  |
| **dotenv**     | Environment Variables | Latest  |

## 📁 Project Structure

```
backend/
├── 📁 src/                          # Source Code
│   ├── 📁 config/                   # Configuration Files
│   │   ├── 📄 database.js           # Database connection & pooling
│   │   └── 📄 environment.js        # Environment validation
│   ├── 📁 controllers/              # Request Handlers
│   │   └── 📄 studentController.js  # Student HTTP operations
│   ├── 📁 models/                   # Database Models
│   │   └── 📄 Student.js            # Student data operations
│   ├── 📁 services/                 # Business Logic
│   │   └── 📄 studentService.js     # Student business rules
│   ├── 📁 routes/                   # API Routes
│   │   └── 📄 studentRoutes.js      # Student endpoints
│   ├── 📁 middleware/               # Middleware Functions
│   │   ├── 📄 errorHandler.js       # Error management
│   │   └── 📄 validation.js         # Input validation
│   └── 📁 utils/                    # Utility Functions
│       ├── 📄 migrationRunner.js    # Database migrations
│       └── 📄 responseHelper.js     # Response formatting
├── 📁 migrations/                   # Database Migrations
│   └── 📄 001_create_students_table.sql
├── 📁 scripts/                      # Management Scripts
│   └── 📄 db-setup.js              # Database setup tool
├── 📄 app.js                       # Main Application
├── 📄 package.json                 # Dependencies & Scripts
├── 📄 .env.example                 # Environment Template
└── 📄 README.md                    # This Documentation
```

## 🚀 Getting Started

### 📋 Prerequisites

Before you begin, ensure you have:

- **Node.js** (version 18 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- **Neon PostgreSQL Account** - [Sign up here](https://neon.tech/)
- **Git** (optional) - [Download here](https://git-scm.com/)

### 📥 Installation

#### Step 1: Clone or Download the Project

```bash
# Option A: Clone with Git
git clone https://github.com/Arindam-saikia-2005/college-project.git
cd college-project/backend

# Option B: Download ZIP and extract
# Then navigate to the backend folder
```

#### Step 2: Install Dependencies

```bash
npm install
```

#### Step 3: Set Up Environment Variables

```bash
# Copy the environment template
cp .env.example .env

# Edit the .env file with your database credentials
# (See Configuration section below)
```

#### Step 4: Set Up Database

```bash
# Create the student table
npm run db:setup
```

#### Step 5: Start the Server

```bash
# Start in development mode
npm start

# You should see:
# 🚀 Server is running on port 3000
# 🌍 Environment: development
# 🔗 CORS Origin: http://localhost:5000
# 📊 Health check: http://localhost:3000/health
```

### ✅ Verify Installation

1. **Check Server Status:**

   ```bash
   curl http://localhost:3000/health
   ```

2. **Check API Information:**

   ```bash
   curl http://localhost:3000/
   ```

3. **Check Database Status:**
   ```bash
   npm run db:status
   ```

## ⚙️ Configuration

### 🔧 Environment Variables

Create a `.env` file in the backend directory with the following variables:

```env
# 🗄️ DATABASE CONFIGURATION (Required)
# Get this from your Neon PostgreSQL dashboard
DATABASE_URL=postgresql://username:password@hostname:port/database?sslmode=require

# 🌐 SERVER CONFIGURATION (Optional)
PORT=3000                           # Server port (default: 3000)
NODE_ENV=development               # Environment (development/production)
CORS_ORIGIN=http://localhost:5000  # Frontend URL for CORS

# 🔗 DATABASE POOL SETTINGS (Optional)
DB_POOL_MIN=2                      # Minimum connections (default: 2)
DB_POOL_MAX=10                     # Maximum connections (default: 10)
DB_POOL_IDLE_TIMEOUT=30000         # Idle timeout in ms (default: 30000)
DB_POOL_CONNECTION_TIMEOUT=2000    # Connection timeout in ms (default: 2000)

# 📝 LOGGING (Optional)
LOG_LEVEL=info                     # Log level (default: info)
```

### 🗄️ Setting Up Neon PostgreSQL

1. **Create Account:**

   - Go to [neon.tech](https://neon.tech/)
   - Sign up for a free account

2. **Create Database:**

   - Create a new project
   - Note down the connection string

3. **Get Connection String:**

   - Go to your project dashboard
   - Copy the connection string
   - It looks like: `postgresql://username:password@hostname:port/database?sslmode=require`

4. **Add to Environment:**
   - Paste the connection string in your `.env` file as `DATABASE_URL`

### 🔧 Alternative Database Configuration

If you prefer individual database parameters instead of a connection string:

```env
# Alternative to DATABASE_URL
DB_HOST=your-neon-hostname.neon.tech
DB_PORT=5432
DB_NAME=your-database-name
DB_USER=your-username
DB_PASSWORD=your-password
DB_SSL=true
```

## 📡 API Documentation

### 🌐 Base URL

```
http://localhost:3000
```

### 📋 Endpoints Overview

| Method | Endpoint              | Description       | Auth Required |
| ------ | --------------------- | ----------------- | ------------- |
| GET    | `/`                   | API information   | ❌            |
| GET    | `/health`             | Health check      | ❌            |
| GET    | `/api/students`       | Get all students  | ❌            |
| POST   | `/api/students`       | Create student    | ❌            |
| GET    | `/api/students/:id`   | Get student by ID | ❌            |
| PUT    | `/api/students/:id`   | Update student    | ❌            |
| DELETE | `/api/students/:id`   | Delete student    | ❌            |
| GET    | `/api/students/stats` | Get statistics    | ❌            |

### 📝 API Examples

#### 1. Create a New Student

```bash
curl -X POST http://localhost:3000/api/students \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "date_of_birth": "2000-01-15",
    "gender": "Male",
    "email": "<EMAIL>",
    "phone_number": "******-0123",
    "address": "123 Main St, City, State",
    "course_applied_for": "Computer Science",
    "qualification": "High School Diploma",
    "marks_percentage": 85.5
  }'
```

**Response:**

```json
{
  "student_id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "date_of_birth": "2000-01-15",
  "gender": "Male",
  "email": "<EMAIL>",
  "phone_number": "******-0123",
  "address": "123 Main St, City, State",
  "course_applied_for": "Computer Science",
  "qualification": "High School Diploma",
  "marks_percentage": "85.50",
  "application_date": "2024-01-15",
  "created_at": "2024-01-15T10:30:00.000Z",
  "updated_at": "2024-01-15T10:30:00.000Z"
}
```

#### 2. Get All Students

```bash
curl http://localhost:3000/api/students
```

#### 3. Get Student by ID

```bash
curl http://localhost:3000/api/students/1
```

#### 4. Update Student

```bash
curl -X PUT http://localhost:3000/api/students/1 \
  -H "Content-Type: application/json" \
  -d '{
    "marks_percentage": 90.0
  }'
```

#### 5. Delete Student

```bash
curl -X DELETE http://localhost:3000/api/students/1
```

#### 6. Get Statistics

```bash
curl http://localhost:3000/api/students/stats
```

**Response:**

```json
{
  "totalStudents": 10,
  "averageMarks": 87.25
}
```

### 🔄 Legacy Endpoints (Backward Compatibility)

| Method | Endpoint             | Modern Equivalent       |
| ------ | -------------------- | ----------------------- |
| POST   | `/api/postData`      | `POST /api/students`    |
| GET    | `/api/fetchData`     | `GET /api/students`     |
| GET    | `/api/fetchById/:id` | `GET /api/students/:id` |

## 💾 Database Schema

### 📊 Student Table Structure

```sql
CREATE TABLE student (
    student_id SERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone_number VARCHAR(15) NOT NULL,
    address TEXT NOT NULL,
    course_applied_for VARCHAR(100) NOT NULL,
    qualification VARCHAR(100) NOT NULL,
    marks_percentage DECIMAL(5,2) NOT NULL,
    application_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 📋 Field Descriptions

| Field                | Type         | Description                 | Constraints                    |
| -------------------- | ------------ | --------------------------- | ------------------------------ |
| `student_id`         | SERIAL       | Unique identifier           | Primary Key, Auto-increment    |
| `first_name`         | VARCHAR(50)  | Student's first name        | Required, Not empty            |
| `last_name`          | VARCHAR(50)  | Student's last name         | Required, Not empty            |
| `date_of_birth`      | DATE         | Birth date                  | Required, Age 16-100           |
| `gender`             | VARCHAR(20)  | Gender identity             | Required, Enum values          |
| `email`              | VARCHAR(255) | Email address               | Required, Unique, Valid format |
| `phone_number`       | VARCHAR(15)  | Contact number              | Required, Valid format         |
| `address`            | TEXT         | Home address                | Required, Not empty            |
| `course_applied_for` | VARCHAR(100) | Desired course              | Required, Not empty            |
| `qualification`      | VARCHAR(100) | Educational background      | Required, Not empty            |
| `marks_percentage`   | DECIMAL(5,2) | Academic percentage         | Required, 0-100                |
| `application_date`   | DATE         | Application submission date | Auto-set to current date       |
| `created_at`         | TIMESTAMP    | Record creation time        | Auto-generated                 |
| `updated_at`         | TIMESTAMP    | Last update time            | Auto-updated                   |

### ✅ Data Validation Rules

- **Age**: Must be between 16-100 years (calculated from date_of_birth)
- **Email**: Must be unique and valid email format
- **Gender**: Must be one of: 'Male', 'Female', 'Other', 'Prefer not to say'
- **Marks**: Must be between 0-100 (decimal allowed)
- **Phone**: Must be 10-15 digits with optional formatting
- **Required Fields**: All fields except application_date are required

## 🔧 Available Commands

### 📦 NPM Scripts

```bash
# 🚀 Development Commands
npm start                    # Start the server
npm run dev                  # Start in development mode (same as start)

# 🗄️ Database Commands
npm run db:setup            # Create student table
npm run db:status           # Check database connection and table status

# 🧪 Testing Commands
npm test                    # Run tests (placeholder)
```

### 🛠️ Manual Commands

```bash
# Database Management
node scripts/db-setup.js setup     # Setup database
node scripts/db-setup.js status    # Check status
node scripts/db-setup.js help      # Show help

# Direct App Execution
node app.js                         # Start application directly
```

## 🧪 Testing the API

### 🔍 Using curl (Command Line)

#### Test Health Check

```bash
curl http://localhost:3000/health
```

#### Test API Information

```bash
curl http://localhost:3000/
```

#### Create Test Student

```bash
curl -X POST http://localhost:3000/api/students \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Test",
    "last_name": "Student",
    "date_of_birth": "2000-01-01",
    "gender": "Other",
    "email": "<EMAIL>",
    "phone_number": "**********",
    "address": "123 Test Street",
    "course_applied_for": "Test Course",
    "qualification": "Test Qualification",
    "marks_percentage": 75.5
  }'
```

### 🌐 Using Browser

Visit these URLs in your browser:

- **API Info**: `http://localhost:3000/`
- **Health Check**: `http://localhost:3000/health`
- **All Students**: `http://localhost:3000/api/students`
- **Student Stats**: `http://localhost:3000/api/students/stats`

### 📱 Using Postman

1. **Import Collection**: Create a new collection in Postman
2. **Set Base URL**: `http://localhost:3000`
3. **Add Requests**: Create requests for each endpoint
4. **Test Scenarios**: Create test cases for validation

## 🔒 Security Features

### 🛡️ Built-in Security

- **SQL Injection Prevention**: All queries use parameterized statements
- **Input Validation**: Comprehensive validation on all inputs
- **Error Handling**: Secure error messages (no sensitive data exposure)
- **CORS Configuration**: Controlled cross-origin access
- **Environment Variables**: Sensitive data stored securely

### 🔐 Data Protection

- **Email Uniqueness**: Prevents duplicate registrations
- **Data Sanitization**: All inputs are cleaned and validated
- **Type Checking**: Strict data type validation
- **Length Limits**: Prevents buffer overflow attacks

### ⚠️ Security Considerations

- **No Authentication**: This is a demo project (add auth for production)
- **Rate Limiting**: Not implemented (consider adding for production)
- **HTTPS**: Use HTTPS in production environments
- **Database Access**: Limit database user permissions

## 🐛 Troubleshooting

### ❌ Common Issues

#### 1. Database Connection Failed

```
❌ Failed to initialize database: connection failed
```

**Solutions:**

- Check your `.env` file has correct `DATABASE_URL`
- Verify Neon PostgreSQL credentials
- Ensure database is running and accessible
- Check network connectivity

#### 2. Port Already in Use

```
Error: listen EADDRINUSE: address already in use :::3000
```

**Solutions:**

```bash
# Find process using port 3000
lsof -i :3000

# Kill the process
kill -9 <PID>

# Or use different port
PORT=3001 npm start
```

#### 3. Module Not Found

```
Error: Cannot find module 'express'
```

**Solutions:**

```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### 4. Environment Variables Not Loaded

```
❌ Missing required environment variables
```

**Solutions:**

- Ensure `.env` file exists in backend directory
- Check `.env` file has all required variables
- Verify `.env` file format (no spaces around =)

#### 5. Table Does Not Exist

```
relation "student" does not exist
```

**Solutions:**

```bash
# Run database setup
npm run db:setup

# Check database status
npm run db:status
```

### 🔧 Debug Mode

Enable detailed logging:

```bash
NODE_ENV=development npm start
```

### 📞 Getting Help

1. **Check Logs**: Look at console output for error details
2. **Verify Environment**: Ensure all prerequisites are installed
3. **Test Database**: Use `npm run db:status` to check connection
4. **Check Network**: Ensure no firewall blocking connections

## 📞 Support

### 📚 Documentation

- **Node.js**: [nodejs.org/docs](https://nodejs.org/docs)
- **Express.js**: [expressjs.com](https://expressjs.com)
- **PostgreSQL**: [postgresql.org/docs](https://postgresql.org/docs)
- **Neon**: [neon.tech/docs](https://neon.tech/docs)

### 🆘 Need Help?

- Check the troubleshooting section above
- Review the API documentation
- Verify your environment setup
- Test with the provided examples

### 🎯 Project Goals

This project demonstrates:

- **Professional Backend Architecture**
- **Database Integration**
- **API Development**
- **Input Validation**
- **Error Handling**
- **Security Best Practices**

---
