# Database Configuration
# Neon PostgreSQL Database URL
# Format: postgresql://username:password@hostname:port/database?sslmode=require
DATABASE_URL=postgresql://username:password@hostname:port/database?sslmode=require

# Alternative individual database configuration (if not using DATABASE_URL)
DB_HOST=your-neon-hostname.neon.tech
DB_PORT=5432
DB_NAME=your-database-name
DB_USER=your-username
DB_PASSWORD=your-password
DB_SSL=true

# Server Configuration
PORT=3000
NODE_ENV=development

# Security Configuration
CORS_ORIGIN=http://localhost:5000

# Database Pool Configuration (Optimized for Cloud Databases like Neon)
DB_POOL_MIN=1
DB_POOL_MAX=5
DB_POOL_IDLE_TIMEOUT=20000
DB_POOL_CONNECTION_TIMEOUT=10000
DB_POOL_ACQUIRE_TIMEOUT=30000
DB_POOL_CREATE_TIMEOUT=10000
DB_POOL_DESTROY_TIMEOUT=5000
DB_POOL_REAP_INTERVAL=1000
DB_POOL_CREATE_RETRY_INTERVAL=200
DB_KEEP_ALIVE=true
DB_KEEP_ALIVE_DELAY=10000

# Logging Configuration
LOG_LEVEL=info
