{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node app.js", "dev": "node app.js", "test": "echo \"Error: no test specified\" && exit 1", "db:setup": "node scripts/db-setup.js setup", "db:status": "node scripts/db-setup.js status"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cors": "2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mongo": "^0.1.0", "pg": "^8.16.0"}}